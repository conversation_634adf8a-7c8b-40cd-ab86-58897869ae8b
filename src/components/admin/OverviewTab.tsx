import React from "react";
import { TabsContent } from "@radix-ui/react-tabs";
import {
  Activity,
  Bot,
  Clock,
  Database,
  DollarSign,
  Key,
  MessageSquare,
  UserPlus,
  Users,
  Zap,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/Card";
import { Session, SystemMetrics, User } from "@/types";
    
const OverviewTab = ({
  systemMetrics,
  users,
  sessions,
}: {
  systemMetrics: SystemMetrics | null;
  users: User[];
  sessions: Session[];
}) => {
 return 
    <div className="grid gap-6">
            <div className="grid gap-6">
              {/* Key Metrics */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Users
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {systemMetrics?.totalUsers || users.length}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {systemMetrics?.activeUsers ||
                        users.filter((u) => u.status === "active").length}{" "}
                      active
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Active Sessions
                    </CardTitle>
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {systemMetrics?.activeSessions ||
                        sessions.filter((s) => s.status === "active").length}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {systemMetrics?.totalSessions || sessions.length} total
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Messages
                    </CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {systemMetrics?.totalMessages?.toLocaleString() || "0"}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {systemMetrics?.totalTokens?.toLocaleString() || "0"}{" "}
                      tokens
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Cost
                    </CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${systemMetrics?.totalCost?.toFixed(2) || "0.00"}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Avg:{" "}
                      {systemMetrics?.averageResponseTime?.toFixed(0) || "0"}ms
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* System Health */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      System Health
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Uptime</span>
                      <span className="text-sm text-green-600">
                        {systemMetrics?.uptime
                          ? `${(systemMetrics.uptime * 100).toFixed(1)}%`
                          : "99.9%"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Error Rate</span>
                      <span className="text-sm text-red-600">
                        {systemMetrics?.errorRate
                          ? `${(systemMetrics.errorRate * 100).toFixed(2)}%`
                          : "0.1%"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Response Time</span>
                      <span className="text-sm text-blue-600">
                        {systemMetrics?.averageResponseTime?.toFixed(0) ||
                          "120"}
                        ms
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Active Connections
                      </span>
                      <span className="text-sm text-purple-600">
                        {systemMetrics?.activeUsers || "1,247"}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Recent Activity
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-green-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            New user registered
                          </p>
                          <p className="text-xs text-muted-foreground">
                            2 minutes ago
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-blue-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">Agent deployed</p>
                          <p className="text-xs text-muted-foreground">
                            15 minutes ago
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-yellow-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            Knowledge base updated
                          </p>
                          <p className="text-xs text-muted-foreground">
                            1 hour ago
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-purple-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">API key created</p>
                          <p className="text-xs text-muted-foreground">
                            2 hours ago
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <Bot className="h-6 w-6" />
                      <span className="text-sm font-medium">Create Agent</span>
                    </Button>
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <Database className="h-6 w-6" />
                      <span className="text-sm font-medium">
                        Add Knowledge Base
                      </span>
                    </Button>
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <Key className="h-6 w-6" />
                      <span className="text-sm font-medium">Add API Key</span>
                    </Button>
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <UserPlus className="h-6 w-6" />
                      <span className="text-sm font-medium">Invite User</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
  );
};

export default OverviewTab;
  