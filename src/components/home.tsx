import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  PlusIcon,
  Settings,
  LogOut,
  MessageSquare,
  Shield,
  Eye,
  EyeOff,
  Bot,
  Database,
  Zap,
  AlertTriangle,
  Moon,
  Sun,
  Monitor,
} from "lucide-react";
import { useTheme } from "./ThemeProvider";
import ChatInterface from "./chat/ChatInterface";
import AdminPanel from "./admin/AdminPanel";
import UserSettingsPanel from "./UserSettingsPanel";
import WebSocketStatusIndicator from "./WebSocketStatusIndicator";
import AuthService from "@/services/auth";
import { AuthState, User } from "@/types";

const Home = () => {
  const { theme, setTheme } = useTheme();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: true,
  });
  const [conversations, setConversations] = useState([
    {
      id: "1",
      title: "Project Planning Discussion",
      timestamp: "2 hours ago",
      preview: "Let's discuss the roadmap for Q3...",
    },
    {
      id: "2",
      title: "Customer Support Query",
      timestamp: "1 day ago",
      preview: "How can I integrate the API with...",
    },
    {
      id: "3",
      title: "Feature Brainstorming",
      timestamp: "3 days ago",
      preview: "I need ideas for improving user engagement...",
    },
  ]);
  const [activeConversation, setActiveConversation] = useState("1");
  const [currentTab, setCurrentTab] = useState("chat");
  const [loginForm, setLoginForm] = useState({
    email: "",
    password: "",
    name: "",
  });
  const [isRegistering, setIsRegistering] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  // Subscribe to auth state changes
  useEffect(() => {
    const unsubscribe = AuthService.subscribe((state) => {
      setAuthState(state);
    });

    // Initialize auth state
    setAuthState(AuthService.getAuthState());

    return unsubscribe;
  }, []);

  // Handle new conversation
  const handleNewConversation = () => {
    const newId = (conversations.length + 1).toString();
    const newConversation = {
      id: newId,
      title: "New Conversation",
      timestamp: "Just now",
      preview: "Start a new conversation...",
    };
    setConversations([newConversation, ...conversations]);
    setActiveConversation(newId);
  };

  // Handle authentication
  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError(null);

    try {
      let result;
      if (isRegistering) {
        result = await AuthService.register(
          loginForm.email,
          loginForm.password,
          loginForm.name,
        );
      } else {
        result = await AuthService.login(loginForm.email, loginForm.password);
      }

      if (!result.success) {
        setAuthError(result.error || "Authentication failed");
      }
    } catch (error) {
      setAuthError("An unexpected error occurred");
    }
  };

  // Handle logout
  const handleLogout = async () => {
    await AuthService.logout();
  };

  // Demo admin login function
  const handleDemoAdminLogin = async () => {
    setAuthError(null);
    try {
      const result = await AuthService.login("<EMAIL>", "admin123");
      if (!result.success) {
        // If demo login fails, create a demo admin session
        const demoUser: User = {
          id: "demo-admin-001",
          email: "<EMAIL>",
          name: "Demo Administrator",
          role: "admin",
          permissions: [
            "admin:read",
            "admin:write",
            "users:manage",
            "system:manage",
          ],
          createdAt: new Date(),
          lastActive: new Date(),
        };

        // Set demo auth state directly
        AuthService.setDemoUser(demoUser);
        setCurrentTab("admin");
      }
    } catch (error) {
      // Fallback to demo mode
      const demoUser: User = {
        id: "demo-admin-001",
        email: "<EMAIL>",
        name: "Demo Administrator",
        role: "admin",
        permissions: [
          "admin:read",
          "admin:write",
          "users:manage",
          "system:manage",
        ],
        createdAt: new Date(),
        lastActive: new Date(),
      };

      AuthService.setDemoUser(demoUser);
      setCurrentTab("admin");
    }
  };

  // Show loading state
  if (authState.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading SynapseAI...</p>
        </div>
      </div>
    );
  }

  // Login form for unauthenticated users
  if (!authState.isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Card className="w-[400px]">
          <CardContent className="pt-6">
            <div className="mb-4 text-center">
              <h2 className="text-2xl font-bold">
                {isRegistering ? "Create Account" : "Login to UAUI"}
              </h2>
              <p className="text-muted-foreground">
                {isRegistering
                  ? "Create your account to access the platform"
                  : "Enter your credentials to access the platform"}
              </p>
            </div>

            {authError && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{authError}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleAuth} className="space-y-4">
              {isRegistering && (
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Enter your full name"
                    value={loginForm.name}
                    onChange={(e) =>
                      setLoginForm((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    required
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={loginForm.email}
                  onChange={(e) =>
                    setLoginForm((prev) => ({ ...prev, email: e.target.value }))
                  }
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={loginForm.password}
                    onChange={(e) =>
                      setLoginForm((prev) => ({
                        ...prev,
                        password: e.target.value,
                      }))
                    }
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={authState.isLoading}
              >
                {authState.isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    {isRegistering ? "Creating Account..." : "Signing In..."}
                  </>
                ) : isRegistering ? (
                  "Create Account"
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>

            <div className="mt-4 text-center space-y-2">
              <Button
                variant="link"
                onClick={() => {
                  setIsRegistering(!isRegistering);
                  setAuthError(null);
                  setLoginForm({ email: "", password: "", name: "" });
                }}
              >
                {isRegistering
                  ? "Already have an account? Sign in"
                  : "Don't have an account? Create one"}
              </Button>

              <div className="border-t pt-4">
                <p className="text-sm text-muted-foreground mb-2">
                  Try the demo:
                </p>
                <Button
                  variant="outline"
                  onClick={handleDemoAdminLogin}
                  className="w-full"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Demo Admin Access
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 border-r bg-card flex flex-col">
        <div className="p-4 border-b">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={handleNewConversation}
          >
            <PlusIcon className="mr-2 h-4 w-4" />
            New Conversation
          </Button>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-2 space-y-2">
            {conversations.map((conversation) => (
              <Card
                key={conversation.id}
                className={`cursor-pointer hover:bg-accent ${activeConversation === conversation.id ? "bg-accent" : ""}`}
                onClick={() => setActiveConversation(conversation.id)}
              >
                <CardContent className="p-3">
                  <div className="font-medium truncate">
                    {conversation.title}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {conversation.timestamp}
                  </div>
                  <div className="text-sm truncate mt-1">
                    {conversation.preview}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>

        <div className="p-4 border-t mt-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Avatar className="h-8 w-8 mr-2">
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${authState.user?.email}`}
                />
                <AvatarFallback>
                  {authState.user?.name?.charAt(0).toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">
                  {authState.user?.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {authState.user?.email}
                </div>
              </div>
            </div>
            <Button variant="ghost" size="icon" onClick={handleLogout}>
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <div className="border-b p-4 flex justify-between items-center">
          <Tabs
            value={currentTab}
            onValueChange={setCurrentTab}
            className="flex-1"
          >
            <TabsList>
              <TabsTrigger value="chat">
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
              {(authState.user?.role === "admin" ||
                authState.user?.role === "agent_builder") && (
                <TabsTrigger value="agents">
                  <Bot className="h-4 w-4 mr-2" />
                  Agents
                </TabsTrigger>
              )}
              {(authState.user?.role === "admin" ||
                authState.user?.role === "knowledge_manager") && (
                <TabsTrigger value="knowledge">
                  <Database className="h-4 w-4 mr-2" />
                  Knowledge
                </TabsTrigger>
              )}
              {authState.user?.role === "admin" && (
                <TabsTrigger value="admin">
                  <Shield className="h-4 w-4 mr-2" />
                  Admin
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>

          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                if (theme === "light") {
                  setTheme("dark");
                } else if (theme === "dark") {
                  setTheme("system");
                } else {
                  setTheme("light");
                }
              }}
              className="h-8 w-8"
            >
              {theme === "light" ? (
                <Sun className="h-4 w-4" />
              ) : theme === "dark" ? (
                <Moon className="h-4 w-4" />
              ) : (
                <Monitor className="h-4 w-4" />
              )}
            </Button>
            <WebSocketStatusIndicator variant="badge" size="sm" />
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="chat" className="h-full">
            <ChatInterface conversationId={activeConversation} />
          </TabsContent>

          <TabsContent value="settings" className="h-full overflow-auto">
            <UserSettingsPanel />
          </TabsContent>

          {(authState.user?.role === "admin" ||
            authState.user?.role === "agent_builder") && (
            <TabsContent value="agents" className="h-full overflow-auto">
              <div className="p-6">
                <h2 className="text-2xl font-bold mb-4">Agent Builder</h2>
                <p className="text-muted-foreground mb-6">
                  Create and manage AI agents with specialized knowledge and
                  capabilities.
                </p>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <Card className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-6 text-center">
                      <Bot className="h-12 w-12 mx-auto mb-4 text-primary" />
                      <h3 className="font-semibold mb-2">Create New Agent</h3>
                      <p className="text-sm text-muted-foreground">
                        Build a custom AI agent with specific knowledge and
                        tools
                      </p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold">Customer Support</h3>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                          Active
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        Handles customer inquiries and support tickets
                      </p>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          Test
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold">Sales Assistant</h3>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                          Draft
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        Assists with product recommendations and sales
                      </p>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          Deploy
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          )}

          {(authState.user?.role === "admin" ||
            authState.user?.role === "knowledge_manager") && (
            <TabsContent value="knowledge" className="h-full overflow-auto">
              <div className="p-6">
                <h2 className="text-2xl font-bold mb-4">
                  Knowledge Management
                </h2>
                <p className="text-muted-foreground mb-6">
                  Manage knowledge bases that provide context and information to
                  your AI agents.
                </p>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <Card className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-6 text-center">
                      <Database className="h-12 w-12 mx-auto mb-4 text-primary" />
                      <h3 className="font-semibold mb-2">
                        Create Knowledge Base
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Upload documents and create a searchable knowledge base
                      </p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold">Product Documentation</h3>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                          Active
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        1,247 documents • Last updated 2 hours ago
                      </p>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          Manage
                        </Button>
                        <Button size="sm" variant="outline">
                          Reindex
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold">FAQ Database</h3>
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                          Indexing
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        523 documents • Processing...
                      </p>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" disabled>
                          Manage
                        </Button>
                        <Button size="sm" variant="outline" disabled>
                          Reindex
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          )}

          {authState.user?.role === "admin" && (
            <TabsContent value="admin" className="h-full">
              <AdminPanel />
            </TabsContent>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
