import { Suspense } from "react";
import { useRoutes, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./components/ThemeProvider";
import Home from "./components/home";
import WebSocketInitializer from "./components/WebSocketInitializer";
import routes from "tempo-routes";

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="synapse-ui-theme">
      <Suspense
        fallback={
          <div className="flex items-center justify-center min-h-screen bg-background">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading SynapseAI...</p>
            </div>
          </div>
        }
      >
        <>
          <WebSocketInitializer />
          <Routes>
            <Route path="/" element={<Home />} />
          </Routes>
          {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
        </>
      </Suspense>
    </ThemeProvider>
  );
}

export default App;
